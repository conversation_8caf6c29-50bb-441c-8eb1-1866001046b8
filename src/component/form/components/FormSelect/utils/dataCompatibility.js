/**
 * FormSelect数据兼容性处理工具
 * 用于处理旧格式数据到新格式数据的转换
 */

/**
 * 判断是否为新格式的值
 * @param {*} value 
 * @returns {boolean}
 */
export function isNewFormat(value) {
  return Array.isArray(value) && value.length > 0 && value[0] && typeof value[0] === 'object' && value[0].id;
}

/**
 * 将旧格式的值转换为新格式
 * @param {string|Array} oldValue 旧格式的值
 * @param {Array} dataSource 数据源数组
 * @param {Array} dataSourceIds ID数组
 * @returns {Array} 新格式的值数组
 */
export function convertOldValueToNewFormat(oldValue, dataSource = [], dataSourceIds = []) {
  if (!oldValue) return [];
  
  // 如果已经是新格式，直接返回
  if (isNewFormat(oldValue)) {
    return oldValue;
  }
  
  // 转换旧格式
  const values = Array.isArray(oldValue) ? oldValue : [oldValue];
  const result = [];
  
  values.forEach(val => {
    // 在dataSource中查找对应的索引
    const index = dataSource.findIndex(item => 
      (typeof item === 'string' ? item : item.value) === val
    );
    
    if (index >= 0) {
      const id = dataSourceIds[index] || `option_${index}`;
      result.push({
        id: id,
        name: val
      });
    }
  });
  
  return result;
}

/**
 * 将新格式的值转换为旧格式（用于向后兼容）
 * @param {Array} newValue 新格式的值数组
 * @param {boolean} isMulti 是否多选
 * @returns {string|Array} 旧格式的值
 */
export function convertNewValueToOldFormat(newValue, isMulti = false) {
  if (!Array.isArray(newValue) || newValue.length === 0) {
    return isMulti ? [] : '';
  }
  
  const oldValues = newValue.map(item => item.name || item.value);
  
  return isMulti ? oldValues : oldValues[0];
}

/**
 * 确保dataSourceIds与dataSource同步
 * @param {Array} dataSource 数据源数组
 * @param {Array} dataSourceIds ID数组
 * @returns {Array} 同步后的ID数组
 */
export function ensureDataSourceIds(dataSource = [], dataSourceIds = []) {
  if (!dataSource.length) return [];
  
  // 如果长度不匹配，重新生成或补齐
  if (dataSourceIds.length !== dataSource.length) {
    const newDataSourceIds = [];
    for (let i = 0; i < dataSource.length; i++) {
      if (dataSourceIds[i]) {
        newDataSourceIds[i] = dataSourceIds[i];
      } else {
        newDataSourceIds[i] = `option_${Date.now()}_${i}`;
      }
    }
    return newDataSourceIds;
  }
  
  return dataSourceIds;
}

/**
 * 生成唯一的选项ID
 * @returns {string}
 */
export function generateOptionId() {
  return `option_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 将dataSource和dataSourceIds转换为options格式
 * @param {Array} dataSource 数据源数组
 * @param {Array} dataSourceIds ID数组
 * @param {string} currentLanguage 当前语言
 * @returns {Array} options数组
 */
export function convertToOptions(dataSource = [], dataSourceIds = [], currentLanguage = 'zh') {
  return dataSource.map((item, index) => {
    const id = dataSourceIds[index] || `option_${index}`;
    
    if (typeof item === 'string') {
      return {
        id: id,
        text: item,
        value: id, // 使用ID作为value，用于el-select的值绑定
        name: item  // 保存原始文本，用于显示和存储
      };
    }
    
    // 如果item已经是对象格式，保持兼容
    return {
      id: item.id || id,
      text: item.text || item.name || item.value,
      value: item.id || id,
      name: item.name || item.text || item.value
    };
  });
}

/**
 * 处理字段的数据兼容性
 * @param {Object} field 字段对象
 * @returns {Object} 处理后的字段对象
 */
export function processFieldCompatibility(field) {
  if (!field || !field.setting) return field;
  
  const setting = field.setting;
  
  // 如果是下拉选择类型
  if (field.formType === 'select' && setting.dataSource) {
    // 确保dataSourceIds存在
    if (!setting.dataSourceIds || setting.dataSourceIds.length !== setting.dataSource.length) {
      setting.dataSourceIds = ensureDataSourceIds(setting.dataSource, setting.dataSourceIds);
    }
    
    // 处理默认值
    if (field.defaultValue && !isNewFormat(field.defaultValue)) {
      field.defaultValue = convertOldValueToNewFormat(
        field.defaultValue, 
        setting.dataSource, 
        setting.dataSourceIds
      );
    }
  }
  
  return field;
}

/**
 * 批量处理字段数组的兼容性
 * @param {Array} fields 字段数组
 * @returns {Array} 处理后的字段数组
 */
export function processFieldsCompatibility(fields = []) {
  return fields.map(field => processFieldCompatibility(field));
}
