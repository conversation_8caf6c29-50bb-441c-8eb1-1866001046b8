<template>
  <div class="form-select">
    <!-- start 下拉模式、标签模式 -->
    <template v-if="selectType==1 || selectType==3">
      <!-- 多选 -->
      <el-select
        :id="`form_${field.fieldName}`"
        :placeholder="placeholder"
        :clearable="clearable"
        :multiple="isMulti"
        :multiple-limit="multiLimt"
        :class="{'select-inline-edit': isSelectInlineEdit }"
        ref="elSelect"
        filterable
        :value="displayValue"
        :disabled="disabled || disabledJustForSelect"
        @change="input"
      >
        <el-option
          v-for="(item, index) in options"
          :key="`${item.value}_${index}`"
          :label="item.text"
          :value="item.value"
          :style="optionStyle"
        >
          <slot name="option" :option="item"></slot>
        </el-option>
      </el-select>
    </template>
    <!-- end 下拉模式 -->

    <!-- start 平铺模式 -->
    <template v-else-if="selectType==2">
      <!-- start 单选 -->
      <el-radio-group v-model="flatDisplayValue" @change="input" v-if="!isMulti">
        <el-radio
          v-for="item in options"
          :label="item.text"
          :key="item.value"
          :value="item.value">
          {{item.text}}
        </el-radio>
      </el-radio-group>
      <!-- end 单选 -->

      <!-- start 多选 -->
      <el-checkbox-group v-model="flatDisplayValue" @change="input" v-if="isMulti">
        <el-checkbox
          v-for="item in options"
          :label="item.text"
          :key="item.id"
          :value="item.value">
          {{item.text}}
        </el-checkbox>
      </el-checkbox-group>
      <!-- end 多选 -->
    </template>
    <!-- end 平铺模式 -->
  </div>
</template>

<script>
import FormMixin from '@src/component/form/mixin/form';
import { isEmpty, isString } from 'pub-bbx-utils';

export default {
  name: 'form-select',
  mixins: [FormMixin],
  props: {
    value: [String, Number, Array],
    source: {
      type: Array
    },
    clearable: {
      type: Boolean,
      default: true
    },
    optionStyle: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    disabledJustForSelect:{
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
    }
  },
  computed: {
    selectType() {
      let setting = this.field.setting || {};
      return setting.selectType || 1;
    },
    isMulti(){
      let setting = this.field.setting || {};
      return setting.isMulti;
    },
    multiLimt() {
      let setting = this.field.setting || {};
      return setting.limit || 0;
    },
    /** 将当前value转换为el-select可用的格式 */
    displayValue() {
      if (!this.value) return this.isMulti ? [] : '';

      // 如果是新格式{id, name}
      if (Array.isArray(this.value) && this.value.length > 0 && this.value[0].id) {
        const ids = this.value.map(item => item.id);
        return this.isMulti ? ids : (ids[0] || '');
      }

      // 如果是旧格式，需要转换为ID
      const oldValues = Array.isArray(this.value) ? this.value : [this.value];
      const ids = [];

      oldValues.forEach(val => {
        const index = this.dataSource.findIndex(item =>
          (typeof item === 'string' ? item : item.value) === val
        );
        if (index >= 0) {
          const id = this.dataSourceIds[index] || `option_${index}`;
          ids.push(id);
        }
      });

      return this.isMulti ? ids : (ids[0] || '');
    },
    /** 平铺模式使用的显示值 */
    flatDisplayValue() {
      return this.displayValue;
    },
    dataSource() {
      let setting = this.field.setting || {};
      return setting.dataSource || [];
    },
    dataSourceIds() {
      let setting = this.field.setting || {};
      return setting.dataSourceIds || [];
    },
    options(){
      // 优先使用source，如果没有则使用dataSource + dataSourceIds生成
      const source = this.source?.filter(s => s && !isEmpty(s.value));
      if (source && source.length > 0) {
        return source;
      }

      // 使用dataSource和dataSourceIds生成options
      const dataSource = this.dataSource.filter(Boolean);
      const dataSourceIds = this.dataSourceIds;

      return dataSource.map((item, index) => {
        const id = dataSourceIds[index] || `option_${index}`;

        if (typeof item === 'string') {
          return {
            id: id,
            text: item,
            value: id, // 使用ID作为value，用于el-select的值绑定
            name: item  // 保存原始文本，用于显示和存储
          }
        }

        // 如果item已经是对象格式，保持兼容
        return {
          id: item.id || id,
          text: item.text || item.name || item.value,
          value: item.id || id,
          name: item.name || item.text || item.value
        };
      });
    }
  },
  mounted() {
    // 兼容旧数据，将旧格式转换为新格式
    if (this.value && !this.isNewFormat(this.value)) {
      const convertedValue = this.convertOldValueToNewFormat(this.value);
      this.$emit('update', {newValue: convertedValue, oldValue: this.value, field: this.field});
    }

    if(this.isMulti && this.value?.length && Array.isArray(this.value)) {
      // fix bug 20153 - 验证选项是否还存在
      let needUpdate = false;
      let correctNewValue = [];

      if (this.isNewFormat(this.value)) {
        // 新格式验证
        correctNewValue = this.value.filter(item => {
          const exists = this.dataSourceIds.includes(item.id);
          if (!exists) needUpdate = true;
          return exists;
        });
      } else {
        // 旧格式验证并转换
        const oldValues = Array.isArray(this.value) ? this.value : [this.value];
        oldValues.forEach(item => {
          const exists = this.dataSource.find((sourceItem=> {
            if(isString(sourceItem)) {
              return sourceItem == item
            }
            return sourceItem?.value == item
          }));
          if (exists) {
            const index = this.dataSource.findIndex(sourceItem => {
              if(isString(sourceItem)) {
                return sourceItem == item
              }
              return sourceItem?.value == item
            });
            const id = this.dataSourceIds[index] || `option_${index}`;
            correctNewValue.push({ id, name: item });
          } else {
            needUpdate = true;
          }
        });
      }

      if (needUpdate) {
        this.$emit('update', {newValue: correctNewValue, oldValue: this.value, field: this.field});
      }
    }
  },
  methods: {
    /** 判断是否为新格式 */
    isNewFormat(value) {
      return Array.isArray(value) && value.length > 0 && value[0] && typeof value[0] === 'object' && value[0].id;
    },

    /** 将选中的ID值转换为{id, name}格式 */
    convertValueToNewFormat(selectedIds) {
      if (!selectedIds) return [];

      const ids = Array.isArray(selectedIds) ? selectedIds : [selectedIds];
      const result = [];

      ids.forEach(id => {
        const option = this.options.find(opt => opt.value === id);
        if (option) {
          result.push({
            id: option.id,
            name: option.name
          });
        }
      });

      return this.isMulti ? result : (result.length > 0 ? result : []);
    },

    /** 兼容旧数据格式的值转换 */
    convertOldValueToNewFormat(oldValue) {
      if (!oldValue) return [];

      // 如果已经是新格式，直接返回
      if (Array.isArray(oldValue) && oldValue.length > 0 && oldValue[0].id) {
        return oldValue;
      }

      // 转换旧格式
      const values = Array.isArray(oldValue) ? oldValue : [oldValue];
      const result = [];

      values.forEach(val => {
        // 在dataSource中查找对应的索引
        const index = this.dataSource.findIndex(item =>
          (typeof item === 'string' ? item : item.value) === val
        );

        if (index >= 0) {
          const id = this.dataSourceIds[index] || `option_${index}`;
          result.push({
            id: id,
            name: val
          });
        }
      });

      return result;
    },

    input(newValue){
      let oldValue = this.value;
      if(this.selectType == 1) this.$refs.elSelect.blur();

      // 将新值转换为{id, name}格式
      const convertedValue = this.convertValueToNewFormat(newValue);

      this.$emit('update', {newValue: convertedValue, oldValue, field: this.field});
      this.$emit('input', convertedValue);
    }
  }
}
</script>


<style lang="scss">
.form-select{
  width: 100%;

  .el-select{
    width: 100%;

    .el-input__inner{
      padding-left: 10px;
    }

    // 超出长度多行显示
    .el-tag {
      height: auto;
      .el-select__tags-text {
        white-space: pre-wrap;
      }
    }
    // 超出单行显示'...'
    // .el-tag {
    //   position: relative;
    //   max-width: 100%;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   .el-select__tags-text {
    //     white-space: nowrap;
    //     text-overflow: ellipsis;
    //     overflow: hidden;
    //     padding-right: 10px;
    //     display: inline-block;
    //     max-width: 100%;
    //   }
    //   .el-tag__close {
    //     position: absolute;
    //     right: 0;
    //     top: 4px;
    //   }
    // }

  }
}
</style>

<style lang="scss" scoped>
.el-radio-group,
.el-checkbox-group {
  width: 100%;

  label {
    width: auto;
    display: inline-block;
    margin-right: 20px;
    padding-left: 0;
  }

  ::v-deep .el-checkbox__label,
  ::v-deep .el-radio__label {
    overflow-wrap: break-word;
    text-overflow: ellipsis; 
    white-space: normal;
    word-break: break-all;
    padding-left: 6px;
  }
}
.select-inline-edit{
  ::v-deep .el-select__tags{
    overflow-x: auto !important;
    scrollbar-width: none; /* 针对Firefox, IE和Edge隐藏滚动条 */
    height: 30px;
    .el-select__input {
      margin-left: 8px;
      background-color: transparent !important;
    }
    &::-webkit-scrollbar {
      display: none; /* 针对WebKit浏览器隐藏滚动条 */
    }
    span{
      display: flex;
      .el-tag{
        .el-select__tags-text{
          word-break: normal;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>