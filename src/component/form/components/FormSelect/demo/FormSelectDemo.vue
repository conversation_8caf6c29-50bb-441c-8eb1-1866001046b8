<template>
  <div class="form-select-demo">
    <h2>FormSelect组件功能演示</h2>
    
    <!-- 新建字段测试（自定义ID） -->
    <div class="demo-section">
      <h3>1. 新建字段（自定义ID）</h3>
      <form-select
        :field="newField"
        :value="newFieldValue"
        @update="handleNewFieldUpdate"
      />
      <div class="value-display">
        <strong>当前值:</strong> {{ JSON.stringify(newFieldValue, null, 2) }}
      </div>
      <div class="field-info">
        <strong>dataSourceIds:</strong> {{ JSON.stringify(newField.setting.dataSourceIds) }}
      </div>
    </div>

    <!-- 默认索引ID测试 -->
    <div class="demo-section">
      <h3>2. 默认索引ID和校验功能</h3>
      <form-select
        :field="indexIdField"
        :value="indexIdValue"
        @update="handleIndexIdUpdate"
      />
      <div class="value-display">
        <strong>当前值:</strong> {{ JSON.stringify(indexIdValue, null, 2) }}
      </div>
      <div class="field-info">
        <strong>dataSourceIds:</strong> {{ JSON.stringify(indexIdField.setting.dataSourceIds) }}
      </div>
      <div class="demo-tips">
        <p><strong>功能说明:</strong></p>
        <ul>
          <li>默认ID从1开始（1, 2, 3...）</li>
          <li>支持清空ID输入框（会自动恢复默认ID）</li>
          <li>重复ID会显示红色边框和错误提示</li>
          <li>ID输入框支持实时校验</li>
        </ul>
      </div>
    </div>
    
    <!-- 旧字段兼容性测试 -->
    <div class="demo-section">
      <h3>3. 旧字段兼容性（单选）</h3>
      <form-select 
        :field="oldSingleField" 
        :value="oldSingleValue"
        @update="handleOldSingleUpdate"
      />
      <div class="value-display">
        <strong>当前值:</strong> {{ JSON.stringify(oldSingleValue, null, 2) }}
      </div>
      <button @click="setOldSingleValue">设置旧格式值</button>
    </div>
    
    <!-- 旧字段多选测试 -->
    <div class="demo-section">
      <h3>4. 旧字段兼容性（多选）</h3>
      <form-select 
        :field="oldMultiField" 
        :value="oldMultiValue"
        @update="handleOldMultiUpdate"
      />
      <div class="value-display">
        <strong>当前值:</strong> {{ JSON.stringify(oldMultiValue, null, 2) }}
      </div>
      <button @click="setOldMultiValue">设置旧格式值</button>
    </div>
    
    <!-- 多语言测试 -->
    <div class="demo-section">
      <h3>5. 多语言支持</h3>
      <form-select 
        :field="multiLangField" 
        :value="multiLangValue"
        @update="handleMultiLangUpdate"
      />
      <div class="value-display">
        <strong>当前值:</strong> {{ JSON.stringify(multiLangValue, null, 2) }}
      </div>
      <div class="language-switch">
        <button @click="switchLanguage('zh')">中文</button>
        <button @click="switchLanguage('en')">English</button>
      </div>
    </div>
    
    <!-- 平铺模式测试 -->
    <div class="demo-section">
      <h3>6. 平铺模式（Radio/Checkbox）</h3>
      <form-select 
        :field="flatField" 
        :value="flatValue"
        @update="handleFlatUpdate"
      />
      <div class="value-display">
        <strong>当前值:</strong> {{ JSON.stringify(flatValue, null, 2) }}
      </div>
    </div>
  </div>
</template>

<script>
import FormSelect from '../FormSelect.vue';

export default {
  name: 'FormSelectDemo',
  components: {
    FormSelect
  },
  data() {
    return {
      currentLanguage: 'zh',
      
      // 新建字段（自定义ID）
      newField: {
        fieldName: 'new_select',
        formType: 'select',
        setting: {
          dataSource: ['新选项1', '新选项2', '新选项3'],
          dataSourceIds: ['custom_1', 'custom_2', 'custom_3'], // 自定义ID
          isMulti: false,
          selectType: 1
        }
      },
      newFieldValue: [],

      // 默认索引ID字段
      indexIdField: {
        fieldName: 'index_id_select',
        formType: 'select',
        setting: {
          dataSource: ['索引选项1', '索引选项2', '索引选项3'],
          dataSourceIds: ['1', '2', '3'], // 使用索引+1作为ID
          isMulti: false,
          selectType: 1
        }
      },
      indexIdValue: [],

      // 旧单选字段
      oldSingleField: {
        fieldName: 'old_single_select',
        formType: 'select',
        setting: {
          dataSource: ['旧选项1', '旧选项2', '旧选项3'],
          // 没有dataSourceIds，模拟旧数据
          isMulti: false,
          selectType: 1
        }
      },
      oldSingleValue: null,
      
      // 旧多选字段
      oldMultiField: {
        fieldName: 'old_multi_select',
        formType: 'select',
        setting: {
          dataSource: ['多选项1', '多选项2', '多选项3'],
          // 没有dataSourceIds，模拟旧数据
          isMulti: true,
          selectType: 1
        }
      },
      oldMultiValue: [],
      
      // 多语言字段
      multiLangField: {
        fieldName: 'multi_lang_select',
        formType: 'select',
        setting: {
          dataSource: ['选项1', '选项2', '选项3'],
          dataSourceIds: ['lang_opt_1', 'lang_opt_2', 'lang_opt_3'],
          dataSourceLanguage: {
            'zh': ['选项1', '选项2', '选项3'],
            'en': ['Option 1', 'Option 2', 'Option 3']
          },
          isMulti: false,
          selectType: 1
        }
      },
      multiLangValue: [],
      
      // 平铺模式字段
      flatField: {
        fieldName: 'flat_select',
        formType: 'select',
        setting: {
          dataSource: ['平铺1', '平铺2', '平铺3'],
          dataSourceIds: ['1', '2', '3'], // 使用简单的数字ID
          isMulti: true,
          selectType: 2 // 平铺模式
        }
      },
      flatValue: []
    };
  },
  methods: {
    handleNewFieldUpdate(event) {
      console.log('新字段更新:', event);
      this.newFieldValue = event.newValue;
    },

    handleIndexIdUpdate(event) {
      console.log('索引ID字段更新:', event);
      this.indexIdValue = event.newValue;
    },
    
    handleOldSingleUpdate(event) {
      console.log('旧单选字段更新:', event);
      this.oldSingleValue = event.newValue;
    },
    
    handleOldMultiUpdate(event) {
      console.log('旧多选字段更新:', event);
      this.oldMultiValue = event.newValue;
    },
    
    handleMultiLangUpdate(event) {
      console.log('多语言字段更新:', event);
      this.multiLangValue = event.newValue;
    },
    
    handleFlatUpdate(event) {
      console.log('平铺模式更新:', event);
      this.flatValue = event.newValue;
    },
    
    setOldSingleValue() {
      // 设置旧格式的单选值
      this.oldSingleValue = '旧选项2';
    },
    
    setOldMultiValue() {
      // 设置旧格式的多选值
      this.oldMultiValue = ['多选项1', '多选项3'];
    },
    
    switchLanguage(lang) {
      this.currentLanguage = lang;
      // 这里可以添加语言切换逻辑
      console.log('切换语言到:', lang);
    }
  }
};
</script>

<style scoped>
.form-select-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.demo-section h3 {
  margin-top: 0;
  color: #333;
}

.value-display {
  margin: 15px 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
}

.field-info {
  margin: 10px 0;
  padding: 8px;
  background-color: #e8f4fd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.language-switch {
  margin-top: 10px;
}

.language-switch button {
  margin-right: 10px;
  padding: 5px 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.language-switch button:hover {
  background-color: #f0f0f0;
}

button {
  padding: 8px 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  margin-top: 10px;
}

button:hover {
  background-color: #f0f0f0;
}

.demo-tips {
  margin-top: 15px;
  padding: 12px;
  background-color: #f8f9fa;
  border-left: 4px solid #409eff;
  border-radius: 4px;
}

.demo-tips p {
  margin: 0 0 8px 0;
  font-weight: bold;
  color: #333;
}

.demo-tips ul {
  margin: 0;
  padding-left: 20px;
}

.demo-tips li {
  margin-bottom: 4px;
  color: #666;
  font-size: 14px;
}
</style>
