/**
 * FormSelect组件测试用例
 * 测试新的ID+name格式支持和兼容性
 */

import { mount } from '@vue/test-utils';
import FormSelect from '../FormSelect.vue';
import { 
  isNewFormat, 
  convertOldValueToNewFormat, 
  convertToOptions,
  ensureDataSourceIds 
} from '../utils/dataCompatibility';

describe('FormSelect组件测试', () => {
  
  // 测试数据兼容性工具函数
  describe('数据兼容性工具函数', () => {
    
    test('isNewFormat - 判断新格式', () => {
      // 新格式
      expect(isNewFormat([{ id: 'opt_1', name: '选项1' }])).toBe(true);
      
      // 旧格式
      expect(isNewFormat('选项1')).toBe(false);
      expect(isNewFormat(['选项1', '选项2'])).toBe(false);
      expect(isNewFormat([])).toBe(false);
      expect(isNewFormat(null)).toBe(false);
    });
    
    test('convertOldValueToNewFormat - 旧格式转新格式', () => {
      const dataSource = ['选项1', '选项2', '选项3'];
      const dataSourceIds = ['1', '2', '3'];
      
      // 单选旧格式转换
      const singleResult = convertOldValueToNewFormat('选项1', dataSource, dataSourceIds);
      expect(singleResult).toEqual([{ id: '1', name: '选项1' }]);

      // 多选旧格式转换
      const multiResult = convertOldValueToNewFormat(['选项1', '选项3'], dataSource, dataSourceIds);
      expect(multiResult).toEqual([
        { id: '1', name: '选项1' },
        { id: '3', name: '选项3' }
      ]);
      
      // 新格式直接返回
      const newFormat = [{ id: '1', name: '选项1' }];
      expect(convertOldValueToNewFormat(newFormat, dataSource, dataSourceIds)).toEqual(newFormat);
    });
    
    test('convertToOptions - 生成options', () => {
      const dataSource = ['选项1', '选项2'];
      const dataSourceIds = ['1', '2'];

      const options = convertToOptions(dataSource, dataSourceIds);
      expect(options).toEqual([
        { id: '1', text: '选项1', value: '1', name: '选项1' },
        { id: '2', text: '选项2', value: '2', name: '选项2' }
      ]);
    });
    
    test('ensureDataSourceIds - 确保ID数组同步', () => {
      const dataSource = ['选项1', '选项2', '选项3'];

      // 没有ID数组时生成（使用索引+1）
      const ids1 = ensureDataSourceIds(dataSource, []);
      expect(ids1).toHaveLength(3);
      expect(ids1[0]).toBe('1');
      expect(ids1[1]).toBe('2');
      expect(ids1[2]).toBe('3');

      // ID数组长度不匹配时补齐
      const existingIds = ['custom_1'];
      const ids2 = ensureDataSourceIds(dataSource, existingIds);
      expect(ids2).toHaveLength(3);
      expect(ids2[0]).toBe('custom_1');
      expect(ids2[1]).toBe('2');
      expect(ids2[2]).toBe('3');
    });
  });
  
  // 测试组件功能
  describe('FormSelect组件功能', () => {
    
    const createWrapper = (props = {}) => {
      const defaultProps = {
        field: {
          fieldName: 'test_select',
          formType: 'select',
          setting: {
            dataSource: ['选项1', '选项2', '选项3'],
            dataSourceIds: ['opt_1', 'opt_2', 'opt_3'],
            isMulti: false
          }
        },
        value: null
      };
      
      return mount(FormSelect, {
        propsData: { ...defaultProps, ...props }
      });
    };
    
    test('组件正常渲染', () => {
      const wrapper = createWrapper();
      expect(wrapper.find('.form-select').exists()).toBe(true);
      expect(wrapper.find('el-select').exists()).toBe(true);
    });
    
    test('options计算属性正确生成', () => {
      const wrapper = createWrapper();
      const options = wrapper.vm.options;
      
      expect(options).toHaveLength(3);
      expect(options[0]).toEqual({
        id: 'opt_1',
        text: '选项1',
        value: 'opt_1',
        name: '选项1'
      });
    });
    
    test('旧格式值自动转换', async () => {
      const wrapper = createWrapper({
        value: '选项1' // 旧格式单选值
      });
      
      // 等待mounted钩子执行
      await wrapper.vm.$nextTick();
      
      // 检查是否触发了update事件
      expect(wrapper.emitted('update')).toBeTruthy();
      const updateEvent = wrapper.emitted('update')[0][0];
      expect(updateEvent.newValue).toEqual([{ id: 'opt_1', name: '选项1' }]);
    });
    
    test('新格式值正常显示', () => {
      const wrapper = createWrapper({
        value: [{ id: 'opt_2', name: '选项2' }]
      });
      
      expect(wrapper.vm.displayValue).toBe('opt_2');
    });
    
    test('多选模式', () => {
      const wrapper = createWrapper({
        field: {
          fieldName: 'test_multi_select',
          formType: 'select',
          setting: {
            dataSource: ['选项1', '选项2', '选项3'],
            dataSourceIds: ['opt_1', 'opt_2', 'opt_3'],
            isMulti: true
          }
        },
        value: [{ id: 'opt_1', name: '选项1' }, { id: 'opt_3', name: '选项3' }]
      });
      
      expect(wrapper.vm.isMulti).toBe(true);
      expect(wrapper.vm.displayValue).toEqual(['opt_1', 'opt_3']);
    });
    
    test('选择事件处理', async () => {
      const wrapper = createWrapper();
      
      // 模拟选择
      await wrapper.vm.input('opt_2');
      
      // 检查事件
      expect(wrapper.emitted('update')).toBeTruthy();
      expect(wrapper.emitted('input')).toBeTruthy();
      
      const updateEvent = wrapper.emitted('update')[0][0];
      expect(updateEvent.newValue).toEqual([{ id: 'opt_2', name: '选项2' }]);
    });
  });
  
  // 测试兼容性场景
  describe('兼容性测试', () => {
    
    test('处理没有dataSourceIds的旧字段', () => {
      const wrapper = createWrapper({
        field: {
          fieldName: 'old_select',
          formType: 'select',
          setting: {
            dataSource: ['选项1', '选项2'],
            // 没有dataSourceIds
            isMulti: false
          }
        }
      });
      
      // dataSourceIds应该自动生成
      expect(wrapper.vm.dataSourceIds).toHaveLength(2);
      expect(wrapper.vm.options).toHaveLength(2);
    });
    
    test('处理旧格式多选值', async () => {
      const wrapper = createWrapper({
        field: {
          fieldName: 'old_multi_select',
          formType: 'select',
          setting: {
            dataSource: ['选项1', '选项2', '选项3'],
            dataSourceIds: ['opt_1', 'opt_2', 'opt_3'],
            isMulti: true
          }
        },
        value: ['选项1', '选项3'] // 旧格式多选值
      });
      
      await wrapper.vm.$nextTick();
      
      // 应该转换为新格式
      const updateEvent = wrapper.emitted('update')[0][0];
      expect(updateEvent.newValue).toEqual([
        { id: 'opt_1', name: '选项1' },
        { id: 'opt_3', name: '选项3' }
      ]);
    });
  });
});
