
<template>
  <div class="form-select">
    <!-- 多选 -->
    <el-select
      v-if="updata"
      :id="`form_${field.fieldName}`"
      popper-class="common-advance-popper"
      style="width: 100%;"
      :placeholder="placeholder"
      :clearable="clearable"
      :multiple="isMulti"
      ref="elSelect"
      filterable
      :value="displayValue"
      @change="input"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.text"
        :value="item.value"
      >
      </el-option>
    </el-select>

  </div>
</template>

<script>
import FormMixin from '@src/component/form/mixin/form';

export default {
  name: 'form-select-search',
  mixins: [FormMixin],
  props: {
    value: [String, Number, Array],
    source: {
      type: Array,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      newValue: this.value,
      updata: true
    };
  },
  watch: {
    isMulti(v) {
      this.updata = false
      setTimeout(() => {
        this.updata = true
      })
    }
  },
  computed: {
    isMulti() {
      let setting = this.field.setting || {};
      return setting.isMulti;
    },
    dataSourceIds() {
      let setting = this.field.setting || {};
      return setting.dataSourceIds || [];
    },
    options() {
      // 优先使用source，如果没有则使用dataSource + dataSourceIds生成
      if (this.source && this.source.length > 0) {
        return this.source;
      }

      let setting = this.field.setting || {};
      let dataSource = setting.dataSource || [];
      let dataSourceIds = this.dataSourceIds;

      return dataSource.map((d, index) => {
        const id = dataSourceIds[index] || `option_${index}`;

        if (typeof d === 'string') {
          return {
            id: id,
            text: d,
            value: id, // 使用ID作为value
            name: d    // 保存原始文本
          };
        }

        return {
          id: d.id || id,
          text: d.text || d.name || d.value,
          value: d.id || id,
          name: d.name || d.text || d.value
        };
      });
    },
    /** 将当前value转换为显示格式 */
    displayValue() {
      if (!this.value) return this.isMulti ? [] : '';

      // 如果是新格式{id, name}
      if (Array.isArray(this.value) && this.value.length > 0 && this.value[0].id) {
        const ids = this.value.map(item => item.id);
        return this.isMulti ? ids : (ids[0] || '');
      }

      // 如果是旧格式，需要转换为ID
      const oldValues = Array.isArray(this.value) ? this.value : [this.value];
      const ids = [];

      oldValues.forEach(val => {
        const setting = this.field.setting || {};
        const dataSource = setting.dataSource || [];
        const index = dataSource.findIndex(item =>
          (typeof item === 'string' ? item : item.value) === val
        );
        if (index >= 0) {
          const id = this.dataSourceIds[index] || `option_${index}`;
          ids.push(id);
        }
      });

      return this.isMulti ? ids : (ids[0] || '');
    },
  },
  methods: {
    /** 将选中的ID值转换为{id, name}格式 */
    convertValueToNewFormat(selectedIds) {
      if (!selectedIds) return [];

      const ids = Array.isArray(selectedIds) ? selectedIds : [selectedIds];
      const result = [];

      ids.forEach(id => {
        const option = this.options.find(opt => opt.value === id);
        if (option) {
          result.push({
            id: option.id,
            name: option.name
          });
        }
      });

      return this.isMulti ? result : (result.length > 0 ? result : []);
    },

    input(newValue) {
      let oldValue = this.value;
      this.$refs.elSelect.blur();

      // 将新值转换为{id, name}格式
      const convertedValue = this.convertValueToNewFormat(newValue);

      this.$emit('update', { newValue: convertedValue, oldValue, field: this.field });
      this.$emit('input', convertedValue);
    },
    setDefaultValue() {
      try {
        const defaultValue = this.field?.defaultValue
        const useDefaultValue = this.field?.useDefaultValue ?? false;
        if (defaultValue && useDefaultValue) {
          this.$emit('update', { newValue: defaultValue, oldValue: null, field: this.field });
          this.$emit('input', defaultValue);
        }
      } catch (error) {
        console.log('catch', error)
      }
    }
  },
  mounted() {
    this.$nextTick(()=>{
      this.setDefaultValue()
    })
  }
};
</script>


<style lang="scss">
.form-select {
  width: 100%;

  .el-select {
    width: 100%;

    .el-input__inner {
      padding-left: 10px;
    }

    // 超出长度多行显示
    .el-tag {
      height: auto;
      .el-select__tags-text {
        white-space: pre-wrap;
      }
    }
    // 超出单行显示'...'
    // .el-tag {
    //   position: relative;
    //   max-width: 100%;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   .el-select__tags-text {
    //     white-space: nowrap;
    //     text-overflow: ellipsis;
    //     overflow: hidden;
    //     padding-right: 10px;
    //     display: inline-block;
    //     max-width: 100%;
    //   }
    //   .el-tag__close {
    //     position: absolute;
    //     right: 0;
    //     top: 4px;
    //   }
    // }
  }
}
</style>
