import _ from 'lodash';
import Platform from '@src/platform';
import {
  SELECT_OPTION_MAX,
  SELECT_OPTION_LENGTH_MAX
} from '@src/component/form/config'
import { getCommonDefaultLangForKey } from '@src/component/util/multiLang/index';
import locales, { t } from '@src/locales'

// 定制化需求，需要从接口获取下拉选项的数量限制
let CUSTOMIZED_SELECT_OPTION_MAX = SELECT_OPTION_MAX;

const FORM_SELECT = {
  name: 'form-select-mixin',
  computed: {
    isSystem() {
      return this.field.isSystem === 1
    },
    options(){
      return this.field.options || [];
    },
  },
  async mounted() {
    if(this.mode === 'event') {
      CUSTOMIZED_SELECT_OPTION_MAX = this.field?.setting?.customizedSelectOptionMax || SELECT_OPTION_MAX;
    }
  },
  methods: {
    /** 生成唯一的选项ID */
    generateOptionId(){
      return `option_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },
    addOption(){
      if(this.options.length >= CUSTOMIZED_SELECT_OPTION_MAX) return Platform.alert(t('common.base.tip.chooseOptionCantMoreThan', {count: CUSTOMIZED_SELECT_OPTION_MAX}));

      let options = _.cloneDeep(this.options);
      this.index++;

      options.push({
        value: ``,
        isDefault: false,
				language: { ...getCommonDefaultLangForKey(), [locales.locale]: '' },
      })

      // 同时更新dataSource和dataSourceIds
      let setting = this.field.setting || {};
      let dataSource = [...(setting.dataSource || []), ''];
      let dataSourceIds = [...(setting.dataSourceIds || []), this.generateOptionId()];

      this.$emit('input', {value: options, prop: 'options'});
      this.$emit('input', {value: dataSource, prop: 'dataSource', isSetting: true});
      this.$emit('input', {value: dataSourceIds, prop: 'dataSourceIds', isSetting: true});
    },
    // 设置默认值(默认和取消默认可以切换)
    setDefaultOption(option){
      if(this.field.isMulti) return Platform.alert(t('common.form.preview.relatedCatalog.des6'));
      if(!option.value) return Platform.alert(t('common.form.preview.relatedCatalog.des7'));
      let _formState = option.isDefault;

      this.options.forEach(item => item.isDefault = false);
      option.isDefault = !_formState;

      // 获取对应的dataSource索引和ID
      let setting = this.field.setting || {};
      let dataSource = setting.dataSource || [];
      let dataSourceIds = setting.dataSourceIds || [];
      let optionIndex = this.options.findIndex(opt => opt === option);

      // 生成新格式的默认值
      let defaultValue = null;
      if (option.isDefault && optionIndex >= 0) {
        let id = dataSourceIds[optionIndex] || `option_${optionIndex}`;
        let name = dataSource[optionIndex] || option.value;
        defaultValue = [{ id, name }];
      }

      this.$emit('input', {value: this.options, prop: 'options'});
      this.$emit('input', {value: defaultValue, prop: 'defaultValue'});
    },
    //下拉多级菜单
    showMultiBatchModal(option,index){   
      if(option.children.length == 0 && index > option.deep ) return this.$message.warning(t('common.base.tip.coverLastOptionTips'));
      
      this.optionText = option.children.map(item => item.value).join('\n');
      this.batchModalShow = true;
      this.errMessage = null;
      this.currentLevel = index;
    },
    showBatchModal(){
      this.optionText = this.field.options.map(item => item.value).join('\n');
      this.batchModalShow = true;
      this.errMessage = null;
    },
    update(value, prop, isSetting = false){
      if(prop == 'isMulti' && this.dataSourceType !== 3) {
        // 如果是多选，清空默认值
        this.options.forEach(item => item.isDefault = false);
        this.$emit('input', {value: this.options, prop: 'options'});
        this.$emit('input', {value: [], prop: 'defaultValue'}); // 使用空数组而不是null
      }
      this.$emit('input', {value, prop, isSetting});
    },
    updateForDom(event){
      let el = event.target;
      let prop = el.dataset.prop;
      let value = el.value;
      
      this.update(value, prop)
    },
    updateOptionText(event, type){
      this.optionText = event.target.value;
      // 多级菜单过滤
      if(type === 'cascader' && this.optionText.indexOf('/') >= 0){
        this.optionText = this.optionText.replace(/\//g, '');
      }
      let newOption = this.optionText.split('\n');
      this.errMessage = this.validateOptions(newOption);
    },
    validateOptions(opts){
      let options = opts[opts.length - 1] == null ? opts.slice(0, -1) : opts;
      let message = [];
    
      // 验证数量
      if(options.length > CUSTOMIZED_SELECT_OPTION_MAX){
        message.push(t('common.base.tip.chooseOptionCantMoreThan', {count: CUSTOMIZED_SELECT_OPTION_MAX}));
      }
    
      // 是否有空白项
      if(options.some(item => !item)){
        message.push(t('common.base.tip.cantHaveNullOption'));
      }
    
      // 验证每一项长度
      let errIndex = options.map((item, index) => item.length > SELECT_OPTION_LENGTH_MAX ? index + 1 : -1).filter(item => item != -1);
      if(errIndex.length > 0){
        message.push(t('common.form.preview.relatedCatalog.des5', {data1: errIndex.join('，'), data2: SELECT_OPTION_LENGTH_MAX}));
      }
    
      return message.length > 0 ? message.join('\n') : null;
    },
  }
}

export default FORM_SELECT;