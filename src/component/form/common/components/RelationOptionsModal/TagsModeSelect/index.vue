<template>
  <div class="tags-mode-select" v-if="value">
    <template v-if="isMulti">
      <el-tooltip placement="top-start" :disabled="!toolTipVal" :content="toolTipVal">
        <BaseTags :value="multiTags"></BaseTags>
      </el-tooltip>
    </template>
    <template v-else>
      <BaseTags :value="tags"></BaseTags>
    </template>
  </div>
</template>
<script>
import BaseTags from '@src/component/common/BaseTags/index.ts';
import { defineComponent, onMounted, ref, watch, computed } from 'vue';
export default defineComponent({
  name: 'tags-mode-select',
  components: {
    BaseTags,
  },
  props: {
    field: {
      type: Object,
      default: () => {},
    },
    value: {
      type: Array | String,
    },
    maxLength: {
      type: Number,
      default: 2
    }
  },
  setup(props) {
    
    const tags = ref([]);
    const multiTags = ref([]);
    const toolTipVal = ref('');

    const maxLength = computed(() => {
      return props.maxLength;
    })

    const isMulti = computed(() => {
      return props.field?.setting?.isMulti;
    })
    const initData = () => {
      if (Array.isArray(props.value) && isMulti.value) {
        // 多选
        const tagsLength = props.value.length;
        let tempList = props.value?.map(val => {
          // 如果是新格式{id, name}，显示name
          const displayValue = (val && typeof val === 'object' && val.name) ? val.name : val;
          return {
            tagName: displayValue,
          };
        });

        if (tagsLength > maxLength.value) {
          const displayValues = props.value.map(val =>
            (val && typeof val === 'object' && val.name) ? val.name : val
          );
          toolTipVal.value = displayValues.join('，');
          tempList = [...tempList.filter((v, i) => i < maxLength.value), ...[{ tagName: `+${tagsLength - maxLength.value}` }]];
        }
        multiTags.value = tempList;
      } else {
        // 单选
        let displayValue = props.value;

        // 如果是新格式{id, name}数组，取第一个的name
        if (Array.isArray(props.value) && props.value.length > 0 && props.value[0] && typeof props.value[0] === 'object' && props.value[0].name) {
          displayValue = props.value[0].name;
        }
        // 如果是新格式{id, name}对象
        else if (props.value && typeof props.value === 'object' && props.value.name) {
          displayValue = props.value.name;
        }

        tags.value = [{ tagName: displayValue }];
      }
    }

    watch(
      () => props.value,
      () => {
        initData()
      },
      {
        immediate: true,
        deep: true
      }
    );

    onMounted(() => {
      initData()
    })
    return {
      tags,
      multiTags,
      toolTipVal,
      isMulti,
    };
  },
});
</script>
<style lang="scss" scoped></style>
